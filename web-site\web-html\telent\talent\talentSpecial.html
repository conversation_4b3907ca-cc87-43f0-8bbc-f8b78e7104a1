<!DOCTYPE html>
<html>

<head>
    <meta name="keywords" content="青创通 · 西宁市创业服务云平台">
    <meta name="description" content="">
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=EmulateIE11">
    <meta http-equiv="Content-Type"
        content="text/html; charset=UTF-8; X-Content-Type-Options=nosniff; X-XSS-Protection: 1;mode=block" />
    <title>找人才-青创通 · 西宁市创业服务云平台</title>
    <!-- base css -->
    <link rel="stylesheet" type="text/css" href="../public/css/zh.min.css" />
    <!-- jbox css -->
    <link rel="stylesheet" type="text/css" href="../public/plugins/jbox/Skins/Blue/jbox.css" />
    <!--分页 css-->
    <link rel="stylesheet" type="text/css" href="../public/plugins/pagination/pagination.css" />
    <!-- common css -->
    <link rel="stylesheet" type="text/css" href="../public/css/common.css" />
    <link rel="stylesheet" type="text/css" href="../public/css/commonNew.css" />
    <!-- this page css -->
    <link rel="stylesheet" type="text/css" href="css/talentSpecial.css?v=202505221517" />
    <link rel="shortcut icon" href="../public/images/icons/favicon.ico" type="image/x-icon" />

    <style>
        /* 加载动画 */
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 招聘信息卡片样式 */
        .job-card-new {
            background: #fff;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border: 1px solid #e6e6e6;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }

        .job-card-new:hover {
            border-color: #0052d9;
            box-shadow: 0 4px 16px rgba(0,82,217,0.15);
            transform: translateY(-2px);
        }

        .job-card-new.matched {
            border-color: #28a745;
            background: linear-gradient(135deg, #fff 0%, #f8fff9 100%);
        }

        .match-score-badge {
            position: absolute;
            top: 15px;
            right: 15px;
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }

        .card-header-new {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 15px;
        }

        .job-title-new {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin: 0;
            flex: 1;
            margin-right: 15px;
        }

        .job-salary-new {
            font-size: 16px;
            font-weight: bold;
            color: #ff6000;
            background: #fff5f0;
            padding: 6px 12px;
            border-radius: 6px;
            white-space: nowrap;
        }

        .job-meta-tags-new {
            display: flex;
            gap: 8px;
            margin-bottom: 15px;
        }

        .meta-tag-new {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            white-space: nowrap;
        }

        .type-tag-new {
            background: #e3f2fd;
            color: #1976d2;
        }

        .location-tag-new {
            background: #f3e5f5;
            color: #7b1fa2;
        }

        .category-tag-new {
            background: #e8f5e8;
            color: #2e7d32;
        }

        .info-grid-new {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 15px;
        }

        .info-item-new {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .info-icon-new {
            font-size: 16px;
            width: 20px;
            text-align: center;
        }

        .info-content-new {
            flex: 1;
        }

        .info-label-new {
            font-size: 12px;
            color: #666;
            margin-bottom: 2px;
        }

        .info-value-new {
            font-size: 14px;
            color: #333;
            font-weight: 500;
        }

        .card-footer-new {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-top: 15px;
            border-top: 1px solid #f0f0f0;
        }

        .publish-time-new {
            font-size: 12px;
            color: #999;
        }

        .view-count-new {
            font-size: 12px;
            color: #999;
        }

        .match-btn-new {
            padding: 8px 16px;
            background: #28a745;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 13px;
            font-weight: 500;
            transition: all 0.3s;
            margin-right: 8px;
        }

        .match-btn-new:hover {
            background: #218838;
            transform: translateY(-1px);
        }

        .smart-match-btn {
            padding: 8px 16px;
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 13px;
            font-weight: 500;
            transition: all 0.3s;
        }

        .smart-match-btn:hover {
            background: linear-gradient(135deg, #0056b3, #004085);
            transform: translateY(-1px);
        }

        .smart-match-btn:active {
            transform: translateY(0);
        }

        .smart-match-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        /* 筛选区域样式优化 */
        .filterBox {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            align-items: center;
        }

        .filterItem {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .filterItem label {
            font-weight: 500;
            color: #495057;
            white-space: nowrap;
            min-width: 80px;
        }

        .filterItem select {
            padding: 8px 12px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            background: white;
            font-size: 14px;
            min-width: 120px;
        }

        .filterItem select:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
        }

        /* 弹框样式 - 确保居中显示 */
        .modal {
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(4px);
        }

        .modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background-color: #fff;
            padding: 0;
            border-radius: 12px;
            width: 90%;
            max-width: 600px;
            max-height: 85vh;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            animation: modalSlideIn 0.3s ease-out;
            display: flex;
            flex-direction: column;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translate(-50%, -60%);
            }
            to {
                opacity: 1;
                transform: translate(-50%, -50%);
            }
        }

        .modal-header {
            padding: 20px 24px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header h2 {
            margin: 0;
            font-size: 20px;
            font-weight: 600;
        }

        .close {
            color: white;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            line-height: 1;
            opacity: 0.8;
            transition: opacity 0.2s;
        }

        .close:hover {
            opacity: 1;
        }

        .modal-body {
            padding: 24px;
            overflow-y: auto;
            line-height: 1.6;
            flex: 1;
            min-height: 0;
        }

        .modal-footer {
            padding: 16px 24px;
            background: #f8f9fa;
            border-top: 1px solid #e9ecef;
            display: flex;
            justify-content: flex-end;
            gap: 12px;
            flex-shrink: 0;
        }

        .modal-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s;
        }

        .modal-btn-primary {
            background: #007bff;
            color: white;
        }

        .modal-btn-primary:hover {
            background: #0056b3;
            transform: translateY(-1px);
        }

        .modal-btn-secondary {
            background: #6c757d;
            color: white;
        }

        .modal-btn-secondary:hover {
            background: #545b62;
        }

        .job-detail-item {
            margin-bottom: 16px;
            padding: 12px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }

        .job-detail-label {
            font-weight: 600;
            color: #495057;
            margin-bottom: 4px;
        }

        .job-detail-value {
            color: #6c757d;
        }

        /* 匹配结果弹框样式 - 重新设计为列表 */
        .match-results {
            width: 100%;
        }

        .job-summary {
            background: #f8f9fa;
            border-radius: 6px;
            padding: 10px;
            margin-bottom: 15px;
            font-size: 13px;
        }

        .job-summary-item {
            display: inline-block;
            margin-right: 15px;
            margin-bottom: 5px;
        }

        .job-summary-item .label {
            font-weight: 600;
            color: #495057;
            margin-right: 5px;
        }

        .job-summary-item .value {
            color: #6c757d;
        }

        .match-loading {
            text-align: center;
            padding: 40px;
        }

        .match-loading .loading-spinner {
            display: inline-block;
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        .match-results-header {
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #e9ecef;
        }

        .match-results-header h3 {
            margin: 0;
            color: #495057;
            font-size: 16px;
        }

        /* 列表样式 - 简单清晰 */
        .match-results-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .worker-item {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            position: relative;
        }

        .worker-item:hover {
            border-color: #007bff;
            box-shadow: 0 2px 8px rgba(0,123,255,0.1);
        }

        .worker-name-row {
            margin-bottom: 10px;
            padding-right: 80px;
        }

        .worker-name {
            font-size: 16px;
            font-weight: 600;
            color: #2c3e50;
            margin: 0;
        }

        .match-percentage {
            position: absolute;
            top: 15px;
            right: 15px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            border: 2px solid rgba(255,255,255,0.2);
        }

        .worker-details {
            margin-bottom: 10px;
        }

        .detail-row {
            margin-bottom: 8px;
            line-height: 1.5;
        }

        .detail-item {
            display: inline-block;
            margin-right: 20px;
            font-size: 13px;
        }

        .detail-label {
            font-weight: 600;
            color: #495057;
            margin-right: 4px;
            font-size: 12px;
            white-space: nowrap;
        }

        .detail-value {
            color: #6c757d;
            font-size: 12px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .worker-stats {
            margin-bottom: 8px;
            line-height: 1.4;
        }

        .stat-item {
            display: inline-block;
            margin-right: 8px;
            margin-bottom: 4px;
            padding: 3px 6px;
            background: #f8f9fa;
            border-radius: 10px;
            font-size: 11px;
            white-space: nowrap;
            vertical-align: top;
        }

        .stat-item.verified {
            background: #e8f5e8;
            color: #2e7d32;
        }

        .stat-icon {
            font-size: 12px;
        }

        .worker-intro {
            background: #f8f9fa;
            padding: 8px;
            border-radius: 6px;
            font-size: 12px;
            line-height: 1.3;
            margin-top: 8px;
            display: block;
        }

        .intro-label {
            font-weight: 600;
            color: #495057;
            margin-right: 4px;
        }

        .intro-text {
            color: #6c757d;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .worker-actions {
            display: flex;
            gap: 12px;
            justify-content: flex-end;
        }

        .action-btn {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s;
        }

        .btn-contact {
            background: #28a745;
            color: white;
        }

        .btn-contact:hover {
            background: #218838;
        }

        .btn-detail {
            background: #007bff;
            color: white;
        }

        .btn-detail:hover {
            background: #0056b3;
        }

        .no-match-results {
            text-align: center;
            padding: 40px;
            color: #6c757d;
        }

        /* 匹配结果样式 */
        .match-results-panel {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-top: 15px;
            border-left: 4px solid #28a745;
        }

        .match-worker-item {
            background: white;
            border-radius: 6px;
            padding: 12px;
            margin-bottom: 10px;
            border: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .worker-info {
            flex: 1;
        }

        .worker-name {
            font-weight: bold;
            color: #333;
            margin-bottom: 4px;
        }

        .worker-skills {
            font-size: 12px;
            color: #666;
        }

        .match-percentage {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            border: 2px solid rgba(255,255,255,0.2);
        }

        .similarity-badge {
            display: inline-block;
            color: white;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            border: 2px solid rgba(255,255,255,0.2);
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .similarity-badge:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }

        .similarity-badge::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .similarity-badge:hover::before {
            left: 100%;
        }

        .similarity-badge.similarity-excellent {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
        }

        .similarity-badge.similarity-good {
            background: linear-gradient(135deg, #8BC34A 0%, #7cb342 100%);
        }

        .similarity-badge.similarity-fair {
            background: linear-gradient(135deg, #FFC107 0%, #ffb300 100%);
        }

        .similarity-badge.similarity-poor {
            background: linear-gradient(135deg, #FF9800 0%, #f57c00 100%);
        }

        .similarity-badge.similarity-bad {
            background: linear-gradient(135deg, #F44336 0%, #d32f2f 100%);
        }

        /* 简洁的相似度圆形显示 */
        .similarity-circle {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            font-size: 12px;
            font-weight: bold;
            color: white;
            text-shadow: 0 1px 2px rgba(0,0,0,0.3);
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .similarity-circle:hover {
            transform: translateY(-1px) scale(1.05);
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }

        .similarity-circle::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            border-radius: 50%;
            background: linear-gradient(45deg, rgba(255,255,255,0.2) 0%, transparent 50%, rgba(255,255,255,0.1) 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .similarity-circle:hover::before {
            opacity: 1;
        }

        /* 不同等级的颜色 */
        .similarity-circle.level-excellent {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
        }

        .similarity-circle.level-good {
            background: linear-gradient(135deg, #8BC34A 0%, #7cb342 100%);
        }

        .similarity-circle.level-fair {
            background: linear-gradient(135deg, #FFC107 0%, #ffb300 100%);
        }

        .similarity-circle.level-poor {
            background: linear-gradient(135deg, #FF9800 0%, #f57c00 100%);
        }

        .similarity-circle.level-bad {
            background: linear-gradient(135deg, #F44336 0%, #d32f2f 100%);
        }

        /* 确保页面底部正确 */
        body {
            margin: 0;
            padding: 0;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .width100 {
            flex: 1;
        }

        #footerBar {
            margin-top: auto;
        }
    </style>
</head>

<body id="viewModelBox">
    <div id="headerBar"></div>
    <!-- banner start -->
    <div class="bannerBox pr">
        <div class="bannerSlide" >
            <img src="./images/ts_bannerBg.jpg" >
        </div>
    </div>
    <!-- banner end -->
    <!-- box1 start -->
    <div class="box5 width100">
        <div class="conAuto1400">
            <!-- 线上招聘会 -->
            <!-- 招聘信息列表 -->
            <div class="">
                <!-- 筛选条件（核心匹配优化版） -->
                <div class="filterBox mb20">
                    <div class="filterItem">
                        <label>关键词搜索：</label>
                        <input type="text" id="jobSearchInput" placeholder="请输入职位名称..."
                               onkeypress="handleSearchKeyPress(event)"
                               style="padding: 8px 12px; border: 1px solid #ced4da; border-radius: 4px; background: white; font-size: 14px; min-width: 200px;">
                    </div>
                    <div class="filterItem">
                        <label>工作类型：</label>
                        <select id="jobTypeFilter" onchange="filterJobs()">
                            <option value="">全部</option>
                            <!-- 动态加载选项 -->
                        </select>
                    </div>
                    <div class="filterItem">
                        <label>工作类别：</label>
                        <select id="jobCategoryFilter" onchange="filterJobs()">
                            <option value="">全部</option>
                            <!-- 动态加载选项 -->
                        </select>
                    </div>
                    <div class="filterItem">
                        <label>薪资类型：</label>
                        <select id="salaryTypeFilter" onchange="filterJobs()">
                            <option value="">全部</option>
                            <!-- 动态加载选项 -->
                        </select>
                    </div>
                    <div class="filterItem">
                        <label>学历要求：</label>
                        <select id="educationFilter" onchange="filterJobs()">
                            <option value="">全部</option>
                            <!-- 动态加载选项 -->
                        </select>
                    </div>
                    <div class="filterItem">
                        <label>&nbsp;</label>
                        <button type="button" class="smart-match-btn" onclick="searchJobs()" style="background: #007bff; cursor: pointer;">
                            <span id="searchBtnText">搜索</span>
                        </button>
                        <button type="button" class="smart-match-btn" onclick="refreshJobPostings()" style="background: #28a745; cursor: pointer; margin-left: 10px;">
                            <span id="refreshBtnText">刷新数据</span>
                        </button>
                        <button type="button" class="smart-match-btn" onclick="debugFilterValues()" style="background: #dc3545; cursor: pointer; margin-left: 10px;">
                            <span>调试参数</span>
                        </button>
                    </div>
                </div>

                <!-- 招聘信息列表 -->
                <div class="jobPostingsList" id="jobPostingsList">

                    <!-- 加载状态 -->
                    <div id="loadingState" style="display: none; text-align: center; padding: 40px;">
                        <div class="loading-spinner" style="display: inline-block; width: 40px; height: 40px; border: 4px solid #f3f3f3; border-top: 4px solid #0052d9; border-radius: 50%; animation: spin 1s linear infinite;"></div>
                        <p style="margin-top: 15px; color: #666;">正在加载招聘信息...</p>
                    </div>

                    <!-- 招聘信息网格 -->
                    <div id="jobGrid" style="display: none;">
                        <!-- 招聘信息卡片将在这里动态生成 -->
                    </div>

                    <!-- 无数据提示 -->
                    <div id="noDataTip" style="display: none; text-align: center; padding: 80px 20px; background: rgba(255,255,255,0.95); border-radius: 12px; box-shadow: 0 4px 20px rgba(0,0,0,0.08); margin: 40px auto; max-width: 400px;">
                        <div style="font-size: 64px; color: #e0e0e0; margin-bottom: 24px; line-height: 1;">📋</div>
                        <h3 style="color: #666; margin-bottom: 12px; font-size: 20px; font-weight: 500;">暂无招聘信息</h3>
                        <p style="color: #999; font-size: 14px; line-height: 1.5; margin: 0;">请调整筛选条件或稍后再试</p>
                    </div>
                </div>

                <!-- 分页 -->
                <div class="pagination-container" id="jobPagination"></div>
            </div>
            <!-- 招聘信息列表 end -->


        </div>
    </div>
     <div id="footerBar"> </div>
    <!-- box6 end -->
    <!--jquery js-->
    <script src="../public/js/jquery-3.5.0.min.js" type="text/javascript" charset="utf-8"></script>
    <script src="../public/plugins/superSlide/jquery.SuperSlide.2.1.3.js" type="text/javascript"
        charset="utf-8"></script>
    <script src="../public/js/words.js" type="text/javascript" charset="utf-8"></script>
    <!--common js-->
    <!-- <script src="../public/js/api.js"></script> -->
    <script src="../public/js/knockout.js" type="text/javascript" charset="utf-8"></script>
    <script src="../public/js/mapping.js" type="text/javascript" charset="utf-8"></script>
    <script type="text/javascript" src="../public/plugins/jbox/jquery.jBox.js"></script>
    <script src="../public/js/utils.js" type="text/javascript" charset="utf-8"></script>
    <script src="../public/js/common.js" type="text/javascript" charset="utf-8"></script>
    <!--this page js  -->
    <script src="js/talentSpecial.js?v=202507231930" type="text/javascript" charset="utf-8"></script>

    <script>
        // 全局变量（核心匹配优化版）
        var jobPostingList = [];
        var searchParams = {
            keyword: '',
            jobType: '',
            jobCategory: '',
            salaryType: '',
            education: '',
            age: ''
        };
        var isPageReady = false;
        var smartMatchEnabled = false;
        var matchResults = {}; // 存储匹配结果

        // 原生Ajax请求函数
        function jobPostingAjaxRequest(url, params, callback) {
            // var baseUrl = 'http://localhost:80/sux-admin/';
            var baseUrl = 'http://************/sux-admin/';

            // 构建查询参数
            var queryString = '';
            if (params && typeof params === 'object') {
                var paramArray = [];
                for (var key in params) {
                    if (params.hasOwnProperty(key) && params[key] !== null && params[key] !== undefined && params[key] !== '') {
                        paramArray.push(encodeURIComponent(key) + '=' + encodeURIComponent(params[key]));
                    }
                }
                queryString = paramArray.length > 0 ? '?' + paramArray.join('&') : '';
            }

            var fullUrl = baseUrl + url + queryString;
            console.log('发送请求到:', fullUrl);
            console.log('请求参数:', params);

            var xhr = new XMLHttpRequest();
            xhr.open('GET', fullUrl, true);
            xhr.timeout = 30000;
            xhr.setRequestHeader('Content-Type', 'application/json');

            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4) {
                    if (xhr.status === 200) {
                        try {
                            var response = JSON.parse(xhr.responseText);
                            if (callback && typeof callback === 'function') {
                                callback(response);
                            }
                        } catch (e) {
                            console.error('解析响应数据失败:', e);
                            // 静默处理解析错误，不显示错误信息
                        }
                    } else {
                        console.error('招聘信息请求失败:', xhr.status, xhr.statusText);
                        // 静默处理请求失败，不显示错误信息
                    }
                }
            };

            xhr.ontimeout = function() {
                console.error('招聘信息请求超时');
                // 静默处理超时，不显示错误信息
            };

            xhr.onerror = function() {
                console.error('招聘信息请求发生错误');
                // 静默处理网络错误，不显示错误信息
            };

            xhr.send();
        }

        // 显示加载状态
        function showLoading() {
            document.getElementById('loadingState').style.display = 'flex';
            document.getElementById('jobGrid').style.display = 'none';
            document.getElementById('noDataTip').style.display = 'none';
        }

        // 隐藏加载状态
        function hideLoading() {
            document.getElementById('loadingState').style.display = 'none';
        }

        // 渲染招聘信息列表
        function renderJobPostingList(jobData) {
            var jobGrid = document.getElementById('jobGrid');
            var noDataTip = document.getElementById('noDataTip');
            var jobCount = document.getElementById('jobCount');

            hideLoading();

            if (!jobData || jobData.length === 0) {
                jobGrid.style.display = 'none';
                noDataTip.style.display = 'flex';
                if (jobCount) jobCount.textContent = '0';
                return;
            }

            var html = '';
            jobData.forEach(function(job) {
                // 格式化薪资显示 - 根据实际数据结构优化
                var salaryText = '面议';
                if (job.salaryMin && job.salaryMax) {
                    var salaryTypeText = getSalaryTypeDisplayName(job.salaryType);
                    if (job.salaryMin === job.salaryMax) {
                        salaryText = '￥' + job.salaryMin + '/' + salaryTypeText;
                    } else {
                        salaryText = '￥' + job.salaryMin + '-' + job.salaryMax + '/' + salaryTypeText;
                    }
                } else if (job.salaryMin) {
                    var salaryTypeText = getSalaryTypeDisplayName(job.salaryType);
                    salaryText = '￥' + job.salaryMin + '+/' + salaryTypeText;
                }

                // 格式化发布时间
                var publishTimeText = '发布时间未知';
                if (job.createTime) {
                    var createDate = new Date(job.createTime);
                    var now = new Date();
                    var diffTime = now - createDate;
                    var diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));

                    if (diffDays === 0) {
                        publishTimeText = '今天发布';
                    } else if (diffDays === 1) {
                        publishTimeText = '昨天发布';
                    } else if (diffDays < 7) {
                        publishTimeText = diffDays + '天前发布';
                    } else {
                        publishTimeText = createDate.toLocaleDateString();
                    }
                }

                // 格式化紧急程度标签
                var urgencyBadge = '';
                if (job.urgencyLevel === 'urgent') {
                    urgencyBadge = '<span class="meta-tag-new" style="background: #ff4757; color: white;">紧急招聘</span>';
                } else if (job.urgencyLevel === 'high') {
                    urgencyBadge = '<span class="meta-tag-new" style="background: #ffa502; color: white;">优先招聘</span>';
                }

                // 检查是否有匹配评分
                var matchScore = matchResults[job.jobId] ? matchResults[job.jobId].similarity : null;
                var cardClass = matchScore ? 'job-card-new matched' : 'job-card-new';
                var matchBadge = matchScore ? `<div class="match-score-badge">匹配度 ${Math.round(matchScore * 100)}%</div>` : '';

                // 职位状态标签
                var statusBadge = '';
                if (job.positionsAvailable && job.positionsFilled) {
                    var remaining = job.positionsAvailable - job.positionsFilled;
                    if (remaining > 0) {
                        statusBadge = `<span class="meta-tag-new" style="background: #2ed573; color: white;">还需${remaining}人</span>`;
                    } else {
                        statusBadge = '<span class="meta-tag-new" style="background: #747d8c; color: white;">已满员</span>';
                    }
                }

                html += `
                    <div class="${cardClass}" onclick="showJobDetails(${job.jobId})">
                        ${matchBadge}
                        <!-- 卡片头部 -->
                        <div class="card-header-new">
                            <h3 class="job-title-new" title="${job.jobTitle || ''}">${job.jobTitle || '--'}</h3>
                            <div class="job-salary-new">${salaryText}</div>
                        </div>

                        <!-- 标签 -->
                        <div class="job-meta-tags-new">
                            <span class="meta-tag-new type-tag-new">${job.jobType || '--'}</span>
                            <span class="meta-tag-new location-tag-new">${job.workLocation || '--'}</span>
                            <span class="meta-tag-new category-tag-new">${job.jobCategory || '--'}</span>
                            ${urgencyBadge}
                            ${statusBadge}
                        </div>

                        <!-- 信息网格 -->
                        <div class="info-grid-new">
                            <div class="info-item-new">
                                <div class="info-icon-new">🏢</div>
                                <div class="info-content-new">
                                    <div class="info-label-new">公司</div>
                                    <div class="info-value-new">${job.companyName || '--'}</div>
                                </div>
                            </div>
                            <div class="info-item-new">
                                <div class="info-icon-new">💼</div>
                                <div class="info-content-new">
                                    <div class="info-label-new">经验要求</div>
                                    <div class="info-value-new">${job.experienceRequired || '--'}</div>
                                </div>
                            </div>
                            <div class="info-item-new">
                                <div class="info-icon-new">🎓</div>
                                <div class="info-content-new">
                                    <div class="info-label-new">学历要求</div>
                                    <div class="info-value-new">${job.educationRequired || '--'}</div>
                                </div>
                            </div>
                            <div class="info-item-new">
                                <div class="info-icon-new">⏰</div>
                                <div class="info-content-new">
                                    <div class="info-label-new">工作时间</div>
                                    <div class="info-value-new">${job.workHoursPerDay || '--'}小时/天</div>
                                </div>
                            </div>
                        </div>

                        <!-- 职位描述 -->
                        ${job.jobDescription ? `
                        <div style="margin: 15px 0; padding: 12px; background: #f8f9fa; border-radius: 6px; border-left: 3px solid #007bff;">
                            <div style="font-size: 12px; color: #666; margin-bottom: 4px;">职位描述</div>
                            <div style="font-size: 13px; color: #333; line-height: 1.4;">${job.jobDescription}</div>
                        </div>
                        ` : ''}

                        <!-- 卡片底部 -->
                        <div class="card-footer-new">
                            <div style="display: flex; gap: 15px; align-items: center;">
                                <span class="publish-time-new">${publishTimeText}</span>
                                <span class="view-count-new">浏览 ${job.viewCount || 0} 次</span>
                                <span class="view-count-new">申请 ${job.applicationCount || 0} 人</span>
                                ${job.contactPerson ? `<span class="view-count-new">联系人: ${job.contactPerson}</span>` : ''}
                            </div>
                            <div>
                                <button class="match-btn-new" onclick="event.stopPropagation(); matchWorkers(${job.jobId})">匹配零工</button>
                            </div>
                        </div>
                    </div>
                `;
            });

            jobGrid.innerHTML = html;
            jobGrid.style.display = 'block';
            noDataTip.style.display = 'none';
            if (jobCount) jobCount.textContent = jobData.length;

            console.log('招聘信息列表渲染完成，共', jobData.length, '条数据');
        }

        // 加载招聘信息列表
        function loadJobPostingList() {
            // 显示加载状态
            showLoading();

            // 更新状态
            if (document.getElementById('loadStatus')) {
                document.getElementById('loadStatus').textContent = '正在加载...';
            }

            var obj = {
                pageSize: 10,
                pageNum: 1
            };

            // 添加筛选条件（核心匹配优化版）
            if (searchParams.keyword) {
                obj.jobTitle = searchParams.keyword; // 使用jobTitle字段进行职位名称搜索
            }
            if (searchParams.jobType) {
                obj.jobType = searchParams.jobType;
            }
            if (searchParams.jobCategory) {
                obj.jobCategory = searchParams.jobCategory;
            }
            if (searchParams.salaryType) {
                obj.salaryType = searchParams.salaryType;
            }
            if (searchParams.education) {
                obj.educationRequired = searchParams.education;
            }
            if (searchParams.age) {
                obj.ageRange = searchParams.age;
            }

            console.log('招聘信息请求参数:', obj);
            console.log('当前搜索条件:', searchParams);
            console.log('实际发送的完整参数:', JSON.stringify(obj, null, 2));

            jobPostingAjaxRequest('public/job/postings', obj, function(data){
                console.log('招聘信息API响应:', data);

                if(data.code == 0 || data.code == 200) {
                    var rows = data.rows || data.data || [];
                    console.log('获取到的招聘信息数据:', rows);

                    // 存储数据并渲染
                    jobPostingList = rows;
                    renderJobPostingList(rows);

                    // 更新状态
                    if (document.getElementById('loadStatus')) {
                        document.getElementById('loadStatus').textContent = 'API加载成功 (' + rows.length + '条)';
                    }
                } else {
                    console.error('获取招聘信息列表失败：', data.msg || data.message);

                    // 如果API失败，尝试使用模拟数据
                    console.log('API失败，使用模拟数据进行展示');
                    loadMockJobPostingsFromProvidedData();

                    // 更新状态
                    if (document.getElementById('loadStatus')) {
                        document.getElementById('loadStatus').textContent = '使用模拟数据 (API暂不可用)';
                    }
                }
            });
        }

        // 使用您提供的实际数据进行展示
        function loadMockJobPostingsFromProvidedData() {
            console.log('加载提供的实际招聘信息数据');
            var providedData = [
                {
                    "createId": null,
                    "createTime": "2025-07-23 22:03:10",
                    "updateId": null,
                    "updateTime": "2025-07-23 22:03:10",
                    "remark": null,
                    "jobId": 4,
                    "jobTitle": "办公楼保洁员",
                    "jobDescription": "负责办公楼日常清洁工作，包括地面清洁、垃圾清理、卫生间清洁等",
                    "jobType": "临时工",
                    "jobCategory": "保洁",
                    "workLocation": "青岛市市北区",
                    "salaryType": "daily",
                    "salaryMin": 120.00,
                    "salaryMax": 150.00,
                    "educationRequired": "不限",
                    "experienceRequired": "无经验要求",
                    "workHoursPerDay": 8,
                    "workDaysPerWeek": 6,
                    "startDate": "2025-07-25",
                    "endDate": "2025-09-30",
                    "contactPerson": "陈主管",
                    "contactPhone": "13800138004",
                    "companyName": "青岛物业管理公司",
                    "urgencyLevel": "urgent",
                    "positionsAvailable": 10,
                    "positionsFilled": 3,
                    "status": "published",
                    "viewCount": 67,
                    "applicationCount": 8,
                    "publisherUserId": 1,
                    "isVerified": 1,
                    "featured": 0,
                    "delFlag": "0"
                },
                {
                    "createId": null,
                    "createTime": "2025-07-23 22:03:10",
                    "updateId": null,
                    "updateTime": "2025-07-23 22:03:10",
                    "remark": null,
                    "jobId": 11,
                    "jobTitle": "快递配送员",
                    "jobDescription": "负责快递包裹的配送工作，要求有电动车或摩托车驾驶证",
                    "jobType": "全职",
                    "jobCategory": "快递员",
                    "workLocation": "青岛市城阳区",
                    "salaryType": "monthly",
                    "salaryMin": 4500.00,
                    "salaryMax": 6000.00,
                    "educationRequired": "不限",
                    "experienceRequired": "有配送经验优先",
                    "workHoursPerDay": 10,
                    "workDaysPerWeek": 6,
                    "startDate": "2025-07-25",
                    "endDate": null,
                    "contactPerson": "郑站长",
                    "contactPhone": "13800138011",
                    "companyName": "顺丰快递青岛站",
                    "urgencyLevel": "urgent",
                    "positionsAvailable": 8,
                    "positionsFilled": 3,
                    "status": "published",
                    "viewCount": 345,
                    "applicationCount": 78,
                    "publisherUserId": 1,
                    "isVerified": 1,
                    "featured": 0,
                    "delFlag": "0"
                },
                {
                    "createId": null,
                    "createTime": "2025-07-23 22:03:10",
                    "updateId": null,
                    "updateTime": "2025-07-23 22:03:10",
                    "remark": null,
                    "jobId": 2,
                    "jobTitle": "快餐店服务员",
                    "jobDescription": "连锁快餐店服务员，负责点餐、收银、清洁等工作，工作轻松，适合学生兼职",
                    "jobType": "兼职",
                    "jobCategory": "服务员",
                    "workLocation": "青岛市李沧区",
                    "salaryType": "hourly",
                    "salaryMin": 18.00,
                    "salaryMax": 22.00,
                    "educationRequired": "不限",
                    "experienceRequired": "无经验要求",
                    "workHoursPerDay": 6,
                    "workDaysPerWeek": 6,
                    "startDate": "2025-07-25",
                    "endDate": "2025-10-31",
                    "contactPerson": "李店长",
                    "contactPhone": "13800138002",
                    "companyName": "麦当劳青岛店",
                    "urgencyLevel": "normal",
                    "positionsAvailable": 8,
                    "positionsFilled": 2,
                    "status": "published",
                    "viewCount": 89,
                    "applicationCount": 15,
                    "publisherUserId": 1,
                    "isVerified": 1,
                    "featured": 0,
                    "delFlag": "0"
                },
                {
                    "createId": null,
                    "createTime": "2025-07-23 22:03:10",
                    "updateId": null,
                    "updateTime": "2025-07-23 22:03:10",
                    "remark": null,
                    "jobId": 5,
                    "jobTitle": "家庭保洁员",
                    "jobDescription": "上门家庭保洁服务，包括房屋清洁、整理收纳等，时间灵活，按次计费",
                    "jobType": "小时工",
                    "jobCategory": "保洁",
                    "workLocation": "青岛市城阳区",
                    "salaryType": "hourly",
                    "salaryMin": 30.00,
                    "salaryMax": 40.00,
                    "educationRequired": "不限",
                    "experienceRequired": "有家政经验优先",
                    "workHoursPerDay": 4,
                    "workDaysPerWeek": 7,
                    "startDate": "2025-07-25",
                    "endDate": null,
                    "contactPerson": "刘经理",
                    "contactPhone": "13800138005",
                    "companyName": "青岛家政服务中心",
                    "urgencyLevel": "normal",
                    "positionsAvailable": 20,
                    "positionsFilled": 5,
                    "status": "published",
                    "viewCount": 145,
                    "applicationCount": 32,
                    "publisherUserId": 1,
                    "isVerified": 1,
                    "featured": 0,
                    "delFlag": "0"
                }
            ];

            // 存储数据并渲染
            jobPostingList = providedData;
            renderJobPostingList(providedData);
            console.log('实际招聘信息数据已设置并渲染，共', providedData.length, '条数据');
        }

        // 模拟招聘信息数据用于测试
        function loadMockJobPostings() {
            console.log('加载模拟招聘信息数据');
            var mockData = [
                {
                    jobId: 1,
                    jobTitle: 'Java高级开发工程师',
                    jobDescription: '负责公司核心业务系统的开发和维护，要求熟练掌握Spring Boot、MyBatis等技术栈',
                    jobType: '全职',
                    jobCategory: 'IT技术',
                    workLocation: '西宁市市南区',
                    salaryMin: 8000,
                    salaryMax: 15000,
                    salaryType: 'monthly',
                    viewCount: 156,
                    createTime: new Date().toISOString(),
                    skillsRequired: 'Java,Spring Boot,MySQL,Redis',
                    experienceRequired: '3-5年',
                    educationRequired: '本科',
                    companyName: '青岛科技有限公司',
                    contactPerson: '张经理',
                    contactPhone: '13800138001'
                }
            ];

            // 存储模拟数据并渲染
            jobPostingList = mockData;
            renderJobPostingList(mockData);
            console.log('模拟招聘信息数据已设置并渲染，共', mockData.length, '条数据');
        }

        // 搜索招聘信息
        function searchJobs() {
            var keyword = document.getElementById('jobSearchInput').value.trim();
            searchParams.keyword = keyword;
            console.log('开始搜索，关键词:', keyword);
            console.log('当前所有搜索参数:', searchParams);

            // 更新搜索按钮状态
            var searchBtn = document.getElementById('searchBtnText');
            if (searchBtn) {
                searchBtn.textContent = '搜索中...';
            }

            loadJobPostingList();

            // 恢复搜索按钮状态
            setTimeout(function() {
                if (searchBtn) {
                    searchBtn.textContent = '搜索';
                }
            }, 1000);
        }

        // 调试函数：检查筛选参数
        function debugFilterValues() {
            console.log('=== 调试筛选参数 ===');

            // 检查HTML元素是否存在
            var elements = {
                jobSearchInput: document.getElementById('jobSearchInput'),
                jobTypeFilter: document.getElementById('jobTypeFilter'),
                jobCategoryFilter: document.getElementById('jobCategoryFilter'),
                salaryTypeFilter: document.getElementById('salaryTypeFilter'),
                educationFilter: document.getElementById('educationFilter'),
                ageFilter: document.getElementById('ageFilter')
            };

            console.log('HTML元素存在性检查:');
            for (var key in elements) {
                console.log(key + ':', elements[key] ? '存在' : '不存在', elements[key] ? '值: "' + elements[key].value + '"' : '');
            }

            console.log('当前searchParams:', searchParams);

            // 模拟构建请求参数
            var testObj = {
                pageSize: 10,
                pageNum: 1
            };

            if (searchParams.keyword) testObj.jobTitle = searchParams.keyword;
            if (searchParams.jobType) testObj.jobType = searchParams.jobType;
            if (searchParams.jobCategory) testObj.jobCategory = searchParams.jobCategory;
            if (searchParams.salaryType) testObj.salaryType = searchParams.salaryType;
            if (searchParams.education) testObj.educationRequired = searchParams.education;
            if (searchParams.age) testObj.ageRange = searchParams.age;

            console.log('将要发送的请求参数:', testObj);
            console.log('=== 调试结束 ===');

            alert('调试信息已输出到控制台，请按F12查看');
        }

        // 处理搜索框回车键
        function handleSearchKeyPress(event) {
            if (event.key === 'Enter' || event.keyCode === 13) {
                event.preventDefault(); // 阻止表单提交
                searchJobs();
            }
        }

        // 筛选招聘信息（核心匹配优化版）
        function filterJobs() {
            var jobType = document.getElementById('jobTypeFilter').value;
            var jobCategory = document.getElementById('jobCategoryFilter').value;
            var salaryType = document.getElementById('salaryTypeFilter').value;
            var education = document.getElementById('educationFilter').value;

            // 安全获取age值，如果元素不存在则为空字符串
            var ageElement = document.getElementById('ageFilter');
            var age = ageElement ? ageElement.value : '';

            searchParams.jobType = jobType;
            searchParams.jobCategory = jobCategory;
            searchParams.salaryType = salaryType;
            searchParams.education = education;
            searchParams.age = age;

            console.log('筛选条件更新:', searchParams);
            console.log('即将发送的请求参数预览:', {
                pageSize: 10,
                pageNum: 1,
                jobTitle: searchParams.keyword,
                jobType: searchParams.jobType,
                jobCategory: searchParams.jobCategory,
                salaryType: searchParams.salaryType,
                educationRequired: searchParams.education,
                ageRange: searchParams.age
            });
            loadJobPostingList();
        }

        // 刷新招聘信息数据
        function refreshJobPostings() {
            console.log('刷新招聘信息数据...');

            // 更新按钮状态
            var refreshBtn = document.getElementById('refreshBtnText');
            if (refreshBtn) {
                refreshBtn.textContent = '刷新中...';
            }

            // 显示加载状态
            showLoading();

            // 保留搜索关键词，只清空其他筛选条件
            var currentKeyword = searchParams.keyword;
            searchParams = {
                keyword: currentKeyword, // 保留搜索关键词
                jobType: '',
                jobCategory: '',
                salaryType: '',
                education: '',
                age: ''
            };

            // 清空筛选下拉框，但不清空搜索框
            if (document.getElementById('jobTypeFilter')) {
                document.getElementById('jobTypeFilter').value = '';
            }
            if (document.getElementById('jobCategoryFilter')) {
                document.getElementById('jobCategoryFilter').value = '';
            }
            if (document.getElementById('salaryTypeFilter')) {
                document.getElementById('salaryTypeFilter').value = '';
            }
            if (document.getElementById('educationFilter')) {
                document.getElementById('educationFilter').value = '';
            }
            if (document.getElementById('ageFilter')) {
                document.getElementById('ageFilter').value = '';
            }

            // 清空匹配结果
            matchResults = {};

            // 重新加载数据
            loadJobPostingList();

            // 恢复按钮状态
            setTimeout(function() {
                if (refreshBtn) {
                    refreshBtn.textContent = '刷新数据';
                }
            }, 1000);
        }

        // 加载筛选选项数据
        function loadFilterOptions() {
            jobPostingAjaxRequest('public/job/filter-options', {}, function(data) {
                if (data.code == 0 || data.code == 200) {
                    var filterData = data.data;

                    // 更新工作类型选项
                    var jobTypeFilter = document.getElementById('jobTypeFilter');
                    if (jobTypeFilter && filterData.jobTypes) {
                        jobTypeFilter.innerHTML = '<option value="">全部</option>';
                        filterData.jobTypes.forEach(function(type) {
                            if (type) {
                                var option = document.createElement('option');
                                option.value = type;
                                option.textContent = type;
                                jobTypeFilter.appendChild(option);
                            }
                        });
                    }

                    // 更新工作类别选项
                    var jobCategoryFilter = document.getElementById('jobCategoryFilter');
                    if (jobCategoryFilter && filterData.jobCategories) {
                        jobCategoryFilter.innerHTML = '<option value="">全部</option>';
                        filterData.jobCategories.forEach(function(category) {
                            if (category) {
                                var option = document.createElement('option');
                                option.value = category;
                                option.textContent = category;
                                jobCategoryFilter.appendChild(option);
                            }
                        });
                    }

                    // 更新薪资类型选项
                    var salaryTypeFilter = document.getElementById('salaryTypeFilter');
                    if (salaryTypeFilter && filterData.salaryTypes) {
                        salaryTypeFilter.innerHTML = '<option value="">全部</option>';
                        filterData.salaryTypes.forEach(function(type) {
                            if (type) {
                                var option = document.createElement('option');
                                option.value = type;
                                option.textContent = getSalaryTypeDisplayName(type);
                                salaryTypeFilter.appendChild(option);
                            }
                        });
                    }

                    // 更新学历要求选项
                    var educationFilter = document.getElementById('educationFilter');
                    if (educationFilter && filterData.educationLevels) {
                        educationFilter.innerHTML = '<option value="">全部</option>';
                        filterData.educationLevels.forEach(function(level) {
                            if (level) {
                                var option = document.createElement('option');
                                option.value = level;
                                option.textContent = level;
                                educationFilter.appendChild(option);
                            }
                        });
                    }
                }
            });
        }

        // 获取薪资类型显示名称
        function getSalaryTypeDisplayName(type) {
            var displayNames = {
                'hourly': '按小时',
                'daily': '按天',
                'monthly': '按月',
                'piece': '按件'
            };
            return displayNames[type] || type;
        }

        // 初始化招聘信息页面
        function initJobPostingsPage() {
            console.log('开始初始化招聘信息页面...');

            // 显示页面加载时间
            if (document.getElementById('loadTime')) {
                document.getElementById('loadTime').textContent = new Date().toLocaleTimeString();
            }

            // 标记页面已准备好
            isPageReady = true;

            console.log('招聘信息页面初始化完成，开始加载数据...');

            // 加载筛选选项数据
            loadFilterOptions();

            // 优先尝试加载API数据，失败则使用提供的实际数据
            loadJobPostingList();
        }

        // 跳转到招聘详情页面
        function goJobDetail(jobId) {
            console.log('跳转到招聘详情页面，jobId:', jobId);
            // 这里可以跳转到招聘详情页面
            // window.open('./jobDetail.html?id=' + jobId);
            alert('跳转到招聘详情页面，ID: ' + jobId);
        }

        // 匹配零工功能（显示匹配弹框）
        function matchWorkers(jobId) {
            console.log('开始匹配零工，招聘信息ID:', jobId);

            // 获取当前招聘信息
            var job = jobPostingList.find(j => j.jobId == jobId);
            if (!job) {
                console.error('招聘信息不存在');
                return;
            }

            // 显示匹配结果弹框
            showMatchResultsModal(job);
        }

        // 显示匹配结果
        function showMatchResults(jobId, workers) {
            var job = jobPostingList.find(j => j.jobId == jobId);
            if (!job) return;

            var html = '<div class="match-results-panel">';
            html += '<h4>为 "' + job.jobTitle + '" 找到 ' + workers.length + ' 个匹配的零工：</h4>';

            if (workers.length === 0) {
                html += '<p style="color: #666; text-align: center; padding: 20px;">暂无匹配的零工</p>';
            } else {
                workers.forEach(function(workerData) {
                    var worker = workerData.worker;
                    var similarity = workerData.similarity || 0;

                    html += '<div class="match-worker-item">';
                    html += '<div class="worker-info">';
                    html += '<div class="worker-name">' + (worker.realName || worker.nickname || '未知') + '</div>';
                    html += '<div class="worker-skills">技能: ' + (worker.skills || '无') + '</div>';
                    html += '<div class="worker-skills">地点: ' + (worker.currentLocation || '未知') + '</div>';

                    // 显示匹配原因
                    if (workerData.matchReasons && workerData.matchReasons.length > 0) {
                        html += '<div class="worker-skills">匹配原因: ' + workerData.matchReasons.join(', ') + '</div>';
                    }

                    html += '</div>';
                    html += '<div class="match-percentage">' + Math.round(similarity * 100) + '%</div>';
                    html += '</div>';
                });
            }

            html += '<div style="text-align: center; margin-top: 15px;">';
            html += '<button onclick="closeMatchResults()" style="padding: 8px 16px; background: #6c757d; color: white; border: none; border-radius: 4px; cursor: pointer;">关闭</button>';
            html += '</div>';
            html += '</div>';

            // 在对应的招聘卡片后面插入匹配结果
            var jobCard = document.querySelector('[onclick="goJobDetail(' + jobId + ')"]');
            if (jobCard) {
                var existingResults = jobCard.nextElementSibling;
                if (existingResults && existingResults.classList.contains('match-results-panel')) {
                    existingResults.remove();
                }
                jobCard.insertAdjacentHTML('afterend', html);
            }
        }

        // 关闭匹配结果
        function closeMatchResults() {
            var panels = document.querySelectorAll('.match-results-panel');
            panels.forEach(function(panel) {
                panel.remove();
            });
        }

        // 显示招聘详情
        function showJobDetails(jobId) {
            var job = jobPostingList.find(j => j.jobId == jobId);
            if (!job) {
                console.error('招聘信息不存在');
                return;
            }

            // 设置弹框标题
            document.getElementById('modalJobTitle').textContent = job.jobTitle || '招聘详情';

            // 构建详情内容
            var content = '';

            content += '<div class="job-detail-item">';
            content += '<div class="job-detail-label">职位名称</div>';
            content += '<div class="job-detail-value">' + (job.jobTitle || '未知') + '</div>';
            content += '</div>';

            content += '<div class="job-detail-item">';
            content += '<div class="job-detail-label">工作类型</div>';
            content += '<div class="job-detail-value">' + (job.jobType || '未知') + '</div>';
            content += '</div>';

            content += '<div class="job-detail-item">';
            content += '<div class="job-detail-label">工作类别</div>';
            content += '<div class="job-detail-value">' + (job.jobCategory || '未知') + '</div>';
            content += '</div>';

            content += '<div class="job-detail-item">';
            content += '<div class="job-detail-label">工作地点</div>';
            content += '<div class="job-detail-value">' + (job.workLocation || '未知') + '</div>';
            content += '</div>';

            content += '<div class="job-detail-item">';
            content += '<div class="job-detail-label">薪资范围</div>';
            var salaryText = '';
            if (job.salaryMin && job.salaryMax) {
                salaryText = '￥' + job.salaryMin + '-' + job.salaryMax + '/' + getSalaryTypeDisplayName(job.salaryType || 'monthly');
            } else if (job.salaryMin) {
                salaryText = '￥' + job.salaryMin + '+/' + getSalaryTypeDisplayName(job.salaryType || 'monthly');
            } else {
                salaryText = '面议';
            }
            content += '<div class="job-detail-value">' + salaryText + '</div>';
            content += '</div>';

            content += '<div class="job-detail-item">';
            content += '<div class="job-detail-label">学历要求</div>';
            content += '<div class="job-detail-value">' + (job.educationRequired || '不限') + '</div>';
            content += '</div>';

            content += '<div class="job-detail-item">';
            content += '<div class="job-detail-label">经验要求</div>';
            content += '<div class="job-detail-value">' + (job.experienceRequired || '不限') + '</div>';
            content += '</div>';

            content += '<div class="job-detail-item">';
            content += '<div class="job-detail-label">公司名称</div>';
            content += '<div class="job-detail-value">' + (job.companyName || '未知') + '</div>';
            content += '</div>';

            content += '<div class="job-detail-item">';
            content += '<div class="job-detail-label">联系人</div>';
            content += '<div class="job-detail-value">' + (job.contactPerson || '未知') + '</div>';
            content += '</div>';

            content += '<div class="job-detail-item">';
            content += '<div class="job-detail-label">联系电话</div>';
            content += '<div class="job-detail-value">' + (job.contactPhone || '未知') + '</div>';
            content += '</div>';

            if (job.jobDescription) {
                content += '<div class="job-detail-item">';
                content += '<div class="job-detail-label">职位描述</div>';
                content += '<div class="job-detail-value">' + job.jobDescription + '</div>';
                content += '</div>';
            }

            // 设置弹框内容
            document.getElementById('modalJobContent').innerHTML = content;

            // 存储当前查看的职位ID，供匹配功能使用
            window.currentJobId = jobId;

            // 显示弹框
            document.getElementById('jobDetailModal').style.display = 'block';
        }

        // 关闭招聘详情弹框
        function closeJobDetailModal() {
            document.getElementById('jobDetailModal').style.display = 'none';
            window.currentJobId = null;
        }

        // 从弹框中匹配零工
        function matchWorkersFromModal() {
            if (window.currentJobId) {
                matchWorkers(window.currentJobId);
                closeJobDetailModal();
            }
        }

        // 点击弹框外部关闭弹框
        window.onclick = function(event) {
            var modal = document.getElementById('jobDetailModal');
            if (event.target == modal) {
                closeJobDetailModal();
            }
        }

        // 刷新招聘数据（删除重复函数，使用上面的主函数）

        // 显示匹配结果弹框
        function showMatchResultsModal(job) {
            // 创建匹配结果弹框
            var modalHtml = `
                <div id="matchResultsModal" class="modal" style="display: block;">
                    <div class="modal-content" style="max-width: 900px; max-height: 90vh;">
                        <div class="modal-header">
                            <h2>匹配零工 - ${job.jobTitle}</h2>
                            <span class="close" onclick="closeMatchResultsModal()">&times;</span>
                        </div>
                        <div class="modal-body" style="max-height: 70vh; overflow-y: auto;">
                            <div class="job-summary">
                                <div class="job-summary-item">
                                    <span class="label">工作类型：</span>
                                    <span class="value">${job.jobType || '未知'}</span>
                                </div>
                                <div class="job-summary-item">
                                    <span class="label">工作类别：</span>
                                    <span class="value">${job.jobCategory || '未知'}</span>
                                </div>
                                <div class="job-summary-item">
                                    <span class="label">薪资类型：</span>
                                    <span class="value">${getSalaryTypeDisplayName(job.salaryType)}</span>
                                </div>
                                <div class="job-summary-item">
                                    <span class="label">学历要求：</span>
                                    <span class="value">${job.educationRequired || '不限'}</span>
                                </div>
                                <div class="job-summary-item">
                                    <span class="label">工作地点：</span>
                                    <span class="value">${job.workLocation || '未知'}</span>
                                </div>
                            </div>
                            <div class="match-loading" id="matchLoading">
                                <div class="loading-spinner"></div>
                                <p>正在匹配零工...</p>
                            </div>
                            <div class="match-results" id="matchResults" style="display: none;">
                                <!-- 匹配结果将在这里显示 -->
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button class="modal-btn modal-btn-secondary" onclick="closeMatchResultsModal()">关闭</button>
                        </div>
                    </div>
                </div>
            `;

            // 添加到页面
            document.body.insertAdjacentHTML('beforeend', modalHtml);

            // 开始匹配
            performWorkerMatching(job);
        }

        // 关闭匹配结果弹框
        function closeMatchResultsModal() {
            var modal = document.getElementById('matchResultsModal');
            if (modal) {
                modal.remove();
            }
        }

        // 执行零工匹配
        function performWorkerMatching(job) {
            jobPostingAjaxRequest('public/job/postings/' + job.jobId + '/match-workers', {limit: 20}, function(data) {
                document.getElementById('matchLoading').style.display = 'none';

                if (data.code == 0 || data.code == 200) {
                    var matchResults = data.data || [];
                    renderMatchResults(matchResults, job);
                } else {
                    console.error('匹配失败：', data.msg);
                    // 使用模拟数据进行匹配
                    var mockResults = generateMockMatchResults(job);
                    renderMatchResults(mockResults, job);
                }
            });
        }

        // 确保在所有资源加载完成后初始化
        window.addEventListener('load', function() {
            console.log('Window load event triggered for talent special page');

            // 初始化招聘信息页面
            initJobPostingsPage();
        });

        // 渲染匹配结果
        function renderMatchResults(matchResults, job) {
            var resultsContainer = document.getElementById('matchResults');

            if (!matchResults || matchResults.length === 0) {
                resultsContainer.innerHTML = `
                    <div class="no-match-results">
                        <div style="text-align: center; padding: 40px;">
                            <div style="font-size: 48px; color: #ddd; margin-bottom: 16px;">👥</div>
                            <h3 style="color: #666; margin-bottom: 8px;">暂无匹配的零工</h3>
                            <p style="color: #999; font-size: 14px;">请尝试调整招聘条件或稍后再试</p>
                        </div>
                    </div>
                `;
                resultsContainer.style.display = 'block';
                return;
            }

            var html = '<div class="match-results-header">';
            html += '<h3>找到 ' + matchResults.length + ' 个匹配的零工</h3>';
            html += '</div>';

            html += '<div class="match-results-list">';

            matchResults.forEach(function(item) {
                var worker = item.worker;
                var similarity = item.similarityPercentage || 0;

                html += '<div class="match-result-item">';

                // 零工基本信息（去掉头像）
                html += '<div class="worker-header">';
                html += '<h4 class="worker-name">' + (worker.realName || worker.nickname || '未知') + '</h4>';
                html += '<div class="similarity-circle level-' + getSimilarityLevel(similarity) + '">';
                html += similarity + '%';
                html += '</div>';
                html += '</div>';

                // 匹配详情 - 简化布局
                html += '<div class="match-details">';
                html += '<div class="match-detail-row">';
                html += '<span class="match-detail-item">';
                html += '<span class="detail-label">类别：</span>';
                html += '<span class="detail-value">' + formatWorkCategories(worker.workCategories) + '</span>';
                html += '</span>';
                html += '<span class="match-detail-item">';
                html += '<span class="detail-label">类型：</span>';
                html += '<span class="detail-value">' + formatJobTypesPreferred(worker.jobTypesPreferred) + '</span>';
                html += '</span>';
                html += '<span class="match-detail-item">';
                html += '<span class="detail-label">学历：</span>';
                html += '<span class="detail-value">' + (worker.educationLevel || '未知') + '</span>';
                html += '</span>';
                html += '<span class="match-detail-item">';
                html += '<span class="detail-label">经验：</span>';
                html += '<span class="detail-value">' + (worker.workExperienceYears || 0) + '年</span>';
                html += '</span>';
                html += '</div>';

                html += '<div class="match-detail-row">';
                html += '<span class="match-detail-item">';
                html += '<span class="detail-label">期望：</span>';
                html += '<span class="detail-value">' + formatWorkerSalaryExpectation(worker) + '</span>';
                html += '</span>';
                html += '<span class="match-detail-item">';
                html += '<span class="detail-label">地区：</span>';
                html += '<span class="detail-value">' + (worker.currentLocation || '未知') + '</span>';
                html += '</span>';
                html += '</div>';

                // 评分信息
                html += '<div class="worker-stats">';
                html += '<span class="stat-item">';
                html += '<span class="stat-icon">⭐</span>';
                html += '<span class="stat-text">' + (worker.ratingAverage || 0) + '分</span>';
                html += '</span>';
                html += '<span class="stat-item">';
                html += '<span class="stat-icon">✅</span>';
                html += '<span class="stat-text">' + (worker.completedJobs || 0) + '个完成</span>';
                html += '</span>';
                html += '<span class="stat-item">';
                html += '<span class="stat-icon">📈</span>';
                html += '<span class="stat-text">' + (worker.successRate || 0) + '%成功率</span>';
                html += '</span>';
                if (worker.isVerified == 1) {
                    html += '<span class="stat-item verified">';
                    html += '<span class="stat-icon">🔒</span>';
                    html += '<span class="stat-text">已认证</span>';
                    html += '</span>';
                }
                html += '</div>';

                // 自我介绍
                if (worker.selfIntroduction) {
                    html += '<div class="worker-intro">';
                    html += '<span class="intro-label">介绍：</span>';
                    html += '<span class="intro-text">' + worker.selfIntroduction + '</span>';
                    html += '</div>';
                }

                html += '</div>';
            });

            html += '</div>';

            resultsContainer.innerHTML = html;
            resultsContainer.style.display = 'block';
        }

        // 获取相似度等级
        function getSimilarityLevel(similarity) {
            if (similarity >= 90) return 'excellent';
            if (similarity >= 80) return 'good';
            if (similarity >= 70) return 'fair';
            if (similarity >= 60) return 'poor';
            return 'bad';
        }

        // 获取相似度颜色 - 使用渐变色
        function getSimilarityColor(similarity) {
            if (similarity >= 90) return 'linear-gradient(135deg, #4CAF50 0%, #45a049 100%)';
            if (similarity >= 80) return 'linear-gradient(135deg, #8BC34A 0%, #7cb342 100%)';
            if (similarity >= 70) return 'linear-gradient(135deg, #FFC107 0%, #ffb300 100%)';
            if (similarity >= 60) return 'linear-gradient(135deg, #FF9800 0%, #f57c00 100%)';
            return 'linear-gradient(135deg, #F44336 0%, #d32f2f 100%)';
        }

        // 格式化工作类别
        function formatWorkCategories(categories) {
            if (!categories) return '未知';
            try {
                var parsed = JSON.parse(categories);
                return Array.isArray(parsed) ? parsed.join(', ') : categories;
            } catch (e) {
                return categories;
            }
        }

        // 格式化偏好工作类型
        function formatJobTypesPreferred(types) {
            if (!types) return '未知';
            try {
                var parsed = JSON.parse(types);
                return Array.isArray(parsed) ? parsed.join(', ') : types;
            } catch (e) {
                return types;
            }
        }

        // 格式化零工薪资期望
        function formatWorkerSalaryExpectation(worker) {
            if (worker.salaryExpectationMin && worker.salaryExpectationMax) {
                return '￥' + worker.salaryExpectationMin + '-' + worker.salaryExpectationMax + '/' + getSalaryTypeDisplayName(worker.salaryTypePreference || 'monthly');
            } else if (worker.salaryExpectationMin) {
                return '￥' + worker.salaryExpectationMin + '+/' + getSalaryTypeDisplayName(worker.salaryTypePreference || 'monthly');
            }
            return '面议';
        }

        // 联系零工
        function contactWorker(workerId, workerName) {
            alert('联系零工：' + workerName + '（ID: ' + workerId + '）\n\n此功能需要完善联系方式的实现。');
        }

        // 查看零工详情
        function viewWorkerDetail(workerId) {
            alert('查看零工详情（ID: ' + workerId + '）\n\n此功能需要完善详情页面的实现。');
        }

        // 生成模拟匹配结果
        function generateMockMatchResults(job) {
            return [
                {
                    worker: {
                        workerId: 101,
                        realName: '张小美',
                        nickname: '美美服务员',
                        workCategories: '["服务员"]',
                        jobTypesPreferred: '["兼职","临时工"]',
                        educationLevel: '高中',
                        workExperienceYears: 2,
                        salaryExpectationMin: 20.00,
                        salaryExpectationMax: 30.00,
                        salaryTypePreference: 'hourly',
                        currentLocation: '西宁市市南区',
                        ratingAverage: 4.8,
                        completedJobs: 18,
                        successRate: 95.5,
                        isVerified: 1,
                        selfIntroduction: '有2年餐厅服务经验，服务态度好，沟通能力强，熟悉收银系统操作，形象气质佳'
                    },
                    similarityPercentage: 92,
                    matchScore: 88
                },
                {
                    worker: {
                        workerId: 104,
                        realName: '刘大姐',
                        nickname: '勤劳刘姐',
                        workCategories: '["保洁"]',
                        jobTypesPreferred: '["全职","兼职","临时工"]',
                        educationLevel: '初中',
                        workExperienceYears: 8,
                        salaryExpectationMin: 25.00,
                        salaryExpectationMax: 35.00,
                        salaryTypePreference: 'hourly',
                        currentLocation: '西宁市市北区',
                        ratingAverage: 4.7,
                        completedJobs: 38,
                        successRate: 94.2,
                        isVerified: 1,
                        selfIntroduction: '有8年保洁经验，工作认真负责，熟悉各种清洁用品使用，客户评价好，持有健康证'
                    },
                    similarityPercentage: 75,
                    matchScore: 78
                }
            ];
        }

    </script>

    <!-- 招聘详情弹框 -->
    <div id="jobDetailModal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="modalJobTitle">招聘详情</h2>
                <span class="close" onclick="closeJobDetailModal()">&times;</span>
            </div>
            <div class="modal-body" id="modalJobContent">
                <!-- 详情内容将在这里动态填充 -->
            </div>
            <div class="modal-footer">
                <button class="modal-btn modal-btn-primary" onclick="matchWorkersFromModal()">匹配零工</button>
                <button class="modal-btn modal-btn-secondary" onclick="closeJobDetailModal()">关闭</button>
            </div>
        </div>
    </div>

    <div id="footerBar"></div>

    <script>
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('页面DOM加载完成，开始初始化...');
            initJobPostingsPage();
        });

        // 兼容旧版本浏览器
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', function() {
                console.log('页面DOM加载完成，开始初始化...');
                initJobPostingsPage();
            });
        } else {
            console.log('页面已加载完成，立即初始化...');
            initJobPostingsPage();
        }
    </script>
</body>

</html>